import React, { useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { CalcDisplay } from './calc/CalcDisplay';
import { CalcButton } from './calc/CalcButton';
import { ModeSelector } from './calc/ModeSelector';

export type CalculatorMode = 'basic' | 'standard' | 'scientific';
export type AngleMode = 'deg' | 'rad';

interface CalculatorState {
  display: string;
  previousValue: number | null;
  operation: string | null;
  waitingForOperand: boolean;
  memory: number;
  angleMode: AngleMode;
  history: string[];
}

const initialState: CalculatorState = {
  display: '0',
  previousValue: null,
  operation: null,
  waitingForOperand: false,
  memory: 0,
  angleMode: 'deg',
  history: []
};

export const Calculator: React.FC = () => {
  const [mode, setMode] = useState<CalculatorMode>('basic');
  const [state, setState] = useState<CalculatorState>(initialState);

  const inputNumber = useCallback((num: string) => {
    setState(prev => {
      if (prev.waitingForOperand) {
        return {
          ...prev,
          display: num,
          waitingForOperand: false
        };
      }
      return {
        ...prev,
        display: prev.display === '0' ? num : prev.display + num
      };
    });
  }, []);

  const inputDot = useCallback(() => {
    setState(prev => {
      if (prev.waitingForOperand) {
        return {
          ...prev,
          display: '0.',
          waitingForOperand: false
        };
      }
      if (prev.display.indexOf('.') === -1) {
        return {
          ...prev,
          display: prev.display + '.'
        };
      }
      return prev;
    });
  }, []);

  const clear = useCallback(() => {
    setState(initialState);
  }, []);

  const clearEntry = useCallback(() => {
    setState(prev => ({
      ...prev,
      display: '0'
    }));
  }, []);

  const backspace = useCallback(() => {
    setState(prev => {
      const display = prev.display;
      if (display.length > 1) {
        return {
          ...prev,
          display: display.slice(0, -1)
        };
      }
      return {
        ...prev,
        display: '0'
      };
    });
  }, []);

  const performCalculation = useCallback((nextOperation?: string) => {
    setState(prev => {
      const inputValue = parseFloat(prev.display);

      if (prev.previousValue === null) {
        return {
          ...prev,
          previousValue: inputValue,
          operation: nextOperation,
          waitingForOperand: true
        };
      }

      const currentOperation = prev.operation;
      const previousValue = prev.previousValue;

      if (!currentOperation || prev.waitingForOperand) {
        return {
          ...prev,
          previousValue: inputValue,
          operation: nextOperation,
          waitingForOperand: true
        };
      }

      let result = previousValue;

      switch (currentOperation) {
        case '+':
          result = previousValue + inputValue;
          break;
        case '-':
          result = previousValue - inputValue;
          break;
        case '×':
          result = previousValue * inputValue;
          break;
        case '÷':
          result = inputValue !== 0 ? previousValue / inputValue : 0;
          break;
        case '=':
          result = inputValue;
          break;
        default:
          return prev;
      }

      const displayResult = result.toString();
      const historyEntry = `${previousValue} ${currentOperation} ${inputValue} = ${result}`;

      return {
        ...prev,
        display: displayResult,
        previousValue: nextOperation ? result : null,
        operation: nextOperation,
        waitingForOperand: true,
        history: [...prev.history, historyEntry].slice(-10)
      };
    });
  }, []);

  const calculate = useCallback(() => {
    performCalculation();
  }, [performCalculation]);

  const operation = useCallback((nextOperation: string) => {
    performCalculation(nextOperation);
  }, [performCalculation]);

  const percentage = useCallback(() => {
    setState(prev => {
      const value = parseFloat(prev.display) / 100;
      return {
        ...prev,
        display: value.toString()
      };
    });
  }, []);

  const toggleSign = useCallback(() => {
    setState(prev => {
      const value = parseFloat(prev.display);
      return {
        ...prev,
        display: (-value).toString()
      };
    });
  }, []);

  // Memory functions
  const memoryAdd = useCallback(() => {
    setState(prev => ({
      ...prev,
      memory: prev.memory + parseFloat(prev.display)
    }));
  }, []);

  const memorySubtract = useCallback(() => {
    setState(prev => ({
      ...prev,
      memory: prev.memory - parseFloat(prev.display)
    }));
  }, []);

  const memoryRecall = useCallback(() => {
    setState(prev => ({
      ...prev,
      display: prev.memory.toString(),
      waitingForOperand: true
    }));
  }, []);

  const memoryClear = useCallback(() => {
    setState(prev => ({
      ...prev,
      memory: 0
    }));
  }, []);

  // Scientific functions
  const scientificFunction = useCallback((func: string) => {
    setState(prev => {
      let value = parseFloat(prev.display);
      let result = value;

      const toRadians = (deg: number) => deg * (Math.PI / 180);
      const toDegrees = (rad: number) => rad * (180 / Math.PI);

      switch (func) {
        case 'sin':
          result = Math.sin(prev.angleMode === 'deg' ? toRadians(value) : value);
          break;
        case 'cos':
          result = Math.cos(prev.angleMode === 'deg' ? toRadians(value) : value);
          break;
        case 'tan':
          result = Math.tan(prev.angleMode === 'deg' ? toRadians(value) : value);
          break;
        case 'asin':
          result = prev.angleMode === 'deg' ? toDegrees(Math.asin(value)) : Math.asin(value);
          break;
        case 'acos':
          result = prev.angleMode === 'deg' ? toDegrees(Math.acos(value)) : Math.acos(value);
          break;
        case 'atan':
          result = prev.angleMode === 'deg' ? toDegrees(Math.atan(value)) : Math.atan(value);
          break;
        case 'log':
          result = Math.log10(value);
          break;
        case 'ln':
          result = Math.log(value);
          break;
        case 'exp':
          result = Math.exp(value);
          break;
        case '10^x':
          result = Math.pow(10, value);
          break;
        case 'x²':
          result = value * value;
          break;
        case 'x³':
          result = value * value * value;
          break;
        case '√':
          result = Math.sqrt(value);
          break;
        case '³√':
          result = Math.cbrt(value);
          break;
        case '1/x':
          result = value !== 0 ? 1 / value : Infinity;
          break;
        case 'x!':
          result = value >= 0 && Number.isInteger(value) ? factorial(value) : NaN;
          break;
        case '!':
          result = value >= 0 && Number.isInteger(value) ? factorial(value) : NaN;
          break;
        case 'π':
          result = Math.PI;
          break;
        case 'e':
          result = Math.E;
          break;
        case 'mod':
          // For modulus, we'll use the previous value
          result = prev.previousValue !== null ? prev.previousValue % value : 0;
          break;
        default:
          return prev;
      }

      return {
        ...prev,
        display: result.toString(),
        waitingForOperand: true
      };
    });
  }, []);

  const factorial = (n: number): number => {
    if (n <= 1) return 1;
    return n * factorial(n - 1);
  };

  const toggleAngleMode = useCallback(() => {
    setState(prev => ({
      ...prev,
      angleMode: prev.angleMode === 'deg' ? 'rad' : 'deg'
    }));
  }, []);

  const getBasicButtons = () => [
    { label: 'C', type: 'clear', span: 1, action: clear },
    { label: 'CE', type: 'clear', span: 1, action: clearEntry },
    { label: '⌫', type: 'clear', span: 1, action: backspace },
    { label: '÷', type: 'operator', span: 1, action: () => operation('÷') },
    
    { label: '7', type: 'number', span: 1, action: () => inputNumber('7') },
    { label: '8', type: 'number', span: 1, action: () => inputNumber('8') },
    { label: '9', type: 'number', span: 1, action: () => inputNumber('9') },
    { label: '×', type: 'operator', span: 1, action: () => operation('×') },
    
    { label: '4', type: 'number', span: 1, action: () => inputNumber('4') },
    { label: '5', type: 'number', span: 1, action: () => inputNumber('5') },
    { label: '6', type: 'number', span: 1, action: () => inputNumber('6') },
    { label: '-', type: 'operator', span: 1, action: () => operation('-') },
    
    { label: '1', type: 'number', span: 1, action: () => inputNumber('1') },
    { label: '2', type: 'number', span: 1, action: () => inputNumber('2') },
    { label: '3', type: 'number', span: 1, action: () => inputNumber('3') },
    { label: '+', type: 'operator', span: 1, action: () => operation('+') },
    
    { label: '±', type: 'operator', span: 1, action: toggleSign },
    { label: '0', type: 'number', span: 1, action: () => inputNumber('0') },
    { label: '.', type: 'number', span: 1, action: inputDot },
    { label: '=', type: 'equals', span: 1, action: calculate }
  ];

  const getStandardButtons = () => [
    { label: 'MC', type: 'memory', span: 1, action: memoryClear },
    { label: 'MR', type: 'memory', span: 1, action: memoryRecall },
    { label: 'M+', type: 'memory', span: 1, action: memoryAdd },
    { label: 'M-', type: 'memory', span: 1, action: memorySubtract },
    
    { label: 'C', type: 'clear', span: 1, action: clear },
    { label: 'CE', type: 'clear', span: 1, action: clearEntry },
    { label: '⌫', type: 'clear', span: 1, action: backspace },
    { label: '÷', type: 'operator', span: 1, action: () => operation('÷') },
    
    { label: '(', type: 'operator', span: 1, action: () => inputNumber('(') },
    { label: ')', type: 'operator', span: 1, action: () => inputNumber(')') },
    { label: '%', type: 'operator', span: 1, action: percentage },
    { label: '×', type: 'operator', span: 1, action: () => operation('×') },
    
    { label: '7', type: 'number', span: 1, action: () => inputNumber('7') },
    { label: '8', type: 'number', span: 1, action: () => inputNumber('8') },
    { label: '9', type: 'number', span: 1, action: () => inputNumber('9') },
    { label: '-', type: 'operator', span: 1, action: () => operation('-') },
    
    { label: '4', type: 'number', span: 1, action: () => inputNumber('4') },
    { label: '5', type: 'number', span: 1, action: () => inputNumber('5') },
    { label: '6', type: 'number', span: 1, action: () => inputNumber('6') },
    { label: '+', type: 'operator', span: 1, action: () => operation('+') },
    
    { label: '1', type: 'number', span: 1, action: () => inputNumber('1') },
    { label: '2', type: 'number', span: 1, action: () => inputNumber('2') },
    { label: '3', type: 'number', span: 1, action: () => inputNumber('3') },
    { label: '±', type: 'operator', span: 1, action: toggleSign },
    
    { label: '0', type: 'number', span: 2, action: () => inputNumber('0') },
    { label: '.', type: 'number', span: 1, action: inputDot },
    { label: '=', type: 'equals', span: 1, action: calculate }
  ];

  const getScientificButtons = () => [
    { label: state.angleMode.toUpperCase(), type: 'scientific', span: 1, action: toggleAngleMode },
    { label: 'sin', type: 'scientific', span: 1, action: () => scientificFunction('sin') },
    { label: 'cos', type: 'scientific', span: 1, action: () => scientificFunction('cos') },
    { label: 'tan', type: 'scientific', span: 1, action: () => scientificFunction('tan') },
    { label: 'ln', type: 'scientific', span: 1, action: () => scientificFunction('ln') },
    { label: 'log', type: 'scientific', span: 1, action: () => scientificFunction('log') },
    
    { label: 'x³', type: 'scientific', span: 1, action: () => scientificFunction('x³') },
    { label: '1/x', type: 'scientific', span: 1, action: () => scientificFunction('1/x') },
    { label: 'MC', type: 'memory', span: 1, action: memoryClear },
    { label: 'MR', type: 'memory', span: 1, action: memoryRecall },
    { label: 'M+', type: 'memory', span: 1, action: memoryAdd },
    { label: 'M-', type: 'memory', span: 1, action: memorySubtract },
    
    { label: 'x²', type: 'scientific', span: 1, action: () => scientificFunction('x²') },
    { label: '√', type: 'scientific', span: 1, action: () => scientificFunction('√') },
    { label: '(', type: 'operator', span: 1, action: () => inputNumber('(') },
    { label: ')', type: 'operator', span: 1, action: () => inputNumber(')') },
    { label: 'C', type: 'clear', span: 1, action: clear },
    { label: '⌫', type: 'clear', span: 1, action: backspace },
    
    { label: 'π', type: 'scientific', span: 1, action: () => scientificFunction('π') },
    { label: 'e', type: 'scientific', span: 1, action: () => scientificFunction('e') },
    { label: '7', type: 'number', span: 1, action: () => inputNumber('7') },
    { label: '8', type: 'number', span: 1, action: () => inputNumber('8') },
    { label: '9', type: 'number', span: 1, action: () => inputNumber('9') },
    { label: '÷', type: 'operator', span: 1, action: () => operation('÷') },
    
    { label: '!', type: 'scientific', span: 1, action: () => scientificFunction('!') },
    { label: 'exp', type: 'scientific', span: 1, action: () => scientificFunction('exp') },
    { label: '4', type: 'number', span: 1, action: () => inputNumber('4') },
    { label: '5', type: 'number', span: 1, action: () => inputNumber('5') },
    { label: '6', type: 'number', span: 1, action: () => inputNumber('6') },
    { label: '×', type: 'operator', span: 1, action: () => operation('×') },
    
    { label: '10^x', type: 'scientific', span: 1, action: () => scientificFunction('10^x') },
    { label: '³√', type: 'scientific', span: 1, action: () => scientificFunction('³√') },
    { label: '1', type: 'number', span: 1, action: () => inputNumber('1') },
    { label: '2', type: 'number', span: 1, action: () => inputNumber('2') },
    { label: '3', type: 'number', span: 1, action: () => inputNumber('3') },
    { label: '-', type: 'operator', span: 1, action: () => operation('-') },
    
    { label: 'asin', type: 'scientific', span: 1, action: () => scientificFunction('asin') },
    { label: 'acos', type: 'scientific', span: 1, action: () => scientificFunction('acos') },
    { label: '0', type: 'number', span: 2, action: () => inputNumber('0') },
    { label: '.', type: 'number', span: 1, action: inputDot },
    { label: '+', type: 'operator', span: 1, action: () => operation('+') },
    
    { label: 'atan', type: 'scientific', span: 1, action: () => scientificFunction('atan') },
    { label: '%', type: 'operator', span: 1, action: percentage },
    { label: '±', type: 'operator', span: 1, action: toggleSign },
    { label: '=', type: 'equals', span: 3, action: calculate }
  ];

  const getButtons = () => {
    switch (mode) {
      case 'basic':
        return getBasicButtons();
      case 'standard':
        return getStandardButtons();
      case 'scientific':
        return getScientificButtons();
      default:
        return getBasicButtons();
    }
  };

  const getGridCols = () => {
    switch (mode) {
      case 'basic':
        return 'grid-cols-4';
      case 'standard':
        return 'grid-cols-4';
      case 'scientific':
        return 'grid-cols-6';
      default:
        return 'grid-cols-4';
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center p-4 relative overflow-hidden">
      <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-accent/5 to-secondary/10" />
      <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-primary/10 rounded-full blur-3xl" />
      <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-accent/10 rounded-full blur-3xl" />
      
      <motion.div
        initial={{ opacity: 0, scale: 0.8, y: 50 }}
        animate={{ opacity: 1, scale: 1, y: 0 }}
        transition={{ duration: 0.6, ease: "easeOut" }}
        className="w-full max-w-md mx-auto relative z-10"
      >
        <motion.div 
          className="calc-gradient-bg rounded-3xl shadow-2xl overflow-hidden border border-white/20 backdrop-blur-xl"
          whileHover={{ scale: 1.02 }}
          transition={{ duration: 0.3 }}
        >
          <div className="p-6 relative">
            <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-transparent" />
            <ModeSelector mode={mode} onModeChange={setMode} />
            <CalcDisplay value={state.display} />
            
            <AnimatePresence mode="wait">
              <motion.div
                key={mode}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.2 }}
                className={`grid ${getGridCols()} gap-3 mt-6`}
              >
                {getButtons().map((button, index) => (
                  <CalcButton
                    key={`${button.label}-${index}`}
                    label={button.label}
                    type={button.type as any}
                    span={button.span}
                    onClick={button.action}
                  />
                ))}
              </motion.div>
            </AnimatePresence>
          </div>
        </motion.div>
      </motion.div>
    </div>
  );
};