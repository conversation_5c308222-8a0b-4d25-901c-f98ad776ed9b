import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';

interface CalcButtonProps {
  label: string;
  type: 'number' | 'operator' | 'equals' | 'clear' | 'memory' | 'scientific';
  span?: number;
  onClick: () => void;
}

export const CalcButton: React.FC<CalcButtonProps> = ({ label, type, span = 1, onClick }) => {
  const getButtonStyles = () => {
    const baseStyles = "h-14 rounded-xl font-bold text-lg transition-all duration-300 active:scale-95 focus:outline-none focus:ring-2 focus:ring-primary/50 calc-button-glow border border-white/10 backdrop-blur-sm relative overflow-hidden";
    
    switch (type) {
      case 'number':
        return cn(baseStyles, "calc-number-gradient text-white shadow-lg hover:shadow-xl");
      case 'operator':
        return cn(baseStyles, "calc-operator-gradient text-white shadow-lg hover:shadow-xl");
      case 'equals':
        return cn(baseStyles, "calc-equals-gradient text-white shadow-lg hover:shadow-xl font-black");
      case 'clear':
        return cn(baseStyles, "calc-clear-gradient text-white shadow-lg hover:shadow-xl");
      case 'memory':
        return cn(baseStyles, "bg-calc-button hover:bg-calc-button-hover text-calc-button-text text-sm border-calc-operator/30");
      case 'scientific':
        return cn(baseStyles, "calc-scientific-gradient text-white text-sm shadow-lg hover:shadow-xl");
      default:
        return cn(baseStyles, "bg-calc-button hover:bg-calc-button-hover text-calc-button-text");
    }
  };

  return (
    <motion.button
      whileHover={{ 
        scale: 1.05,
        rotateZ: type === 'equals' ? 2 : 0,
        transition: { duration: 0.2 }
      }}
      whileTap={{ scale: 0.95 }}
      className={cn(
        getButtonStyles(),
        span > 1 && `col-span-${span}`
      )}
      onClick={onClick}
    >
      <motion.div
        className="absolute inset-0 bg-white/10 opacity-0 rounded-xl"
        whileHover={{ opacity: 1 }}
        transition={{ duration: 0.3 }}
      />
      <span className="relative z-10">{label}</span>
    </motion.button>
  );
};