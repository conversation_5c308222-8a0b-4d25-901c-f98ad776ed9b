import React from 'react';
import { motion } from 'framer-motion';

interface CalcDisplayProps {
  value: string;
}

export const CalcDisplay: React.FC<CalcDisplayProps> = ({ value }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      className="calc-display-gradient rounded-2xl p-6 mt-6 calc-display-glow border border-white/20 backdrop-blur-md relative overflow-hidden"
    >
      <div className="absolute inset-0 bg-gradient-to-r from-primary/10 to-accent/10 opacity-50" />
      <motion.div
        key={value}
        initial={{ scale: 1.1, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        transition={{ duration: 0.2, ease: "easeOut" }}
        className="text-right relative z-10"
      >
        <div className="text-4xl md:text-5xl font-mono font-bold text-calc-display-text overflow-hidden tracking-wider">
          <motion.span
            className="inline-block"
            animate={{ 
              textShadow: "0 0 20px hsl(280 100% 70% / 0.6)"
            }}
            transition={{ duration: 0.3 }}
          >
            {value}
          </motion.span>
        </div>
      </motion.div>
    </motion.div>
  );
};