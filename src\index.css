@tailwind base;
@tailwind components;
@tailwind utilities;

/* Calculator Design System - Vibrant, Beautiful & Interactive */

@layer base {
  :root {
    --background: 240 100% 99%;
    --foreground: 220 9% 15%;

    --card: 0 0% 100%;
    --card-foreground: 220 9% 15%;

    --popover: 0 0% 100%;
    --popover-foreground: 220 9% 15%;

    --primary: 280 100% 70%;
    --primary-foreground: 0 0% 100%;

    --secondary: 240 60% 95%;
    --secondary-foreground: 220 9% 15%;

    --muted: 240 60% 95%;
    --muted-foreground: 220 9% 46%;

    --accent: 320 100% 75%;
    --accent-foreground: 0 0% 100%;

    --destructive: 0 100% 67%;
    --destructive-foreground: 0 0% 100%;

    --border: 240 30% 90%;
    --input: 240 30% 90%;
    --ring: 280 100% 70%;

    /* Subtle Calculator Colors */
    --calc-display: 240 40% 8%;
    --calc-display-text: 220 15% 85%;
    --calc-body: 240 20% 15%;
    --calc-button: 240 15% 20%;
    --calc-button-hover: 240 20% 25%;
    --calc-button-text: 0 0% 95%;
    --calc-number: 220 25% 50%;
    --calc-number-hover: 220 30% 55%;
    --calc-operator: 240 40% 55%;
    --calc-operator-hover: 240 45% 60%;
    --calc-equals: 160 40% 45%;
    --calc-equals-hover: 160 45% 50%;
    --calc-clear: 15 50% 55%;
    --calc-clear-hover: 15 55% 60%;
    --calc-scientific: 200 30% 50%;
    --calc-scientific-hover: 200 35% 55%;

    /* Subtle Gradient Variables */
    --gradient-primary: linear-gradient(135deg, hsl(240 40% 55%), hsl(240 45% 60%));
    --gradient-display: linear-gradient(135deg, hsl(240 40% 8%), hsl(240 35% 12%));
    --gradient-body: linear-gradient(135deg, hsl(240 20% 15%), hsl(240 25% 18%));
    --gradient-number: linear-gradient(135deg, hsl(220 25% 50%), hsl(220 30% 55%));
    --gradient-operator: linear-gradient(135deg, hsl(240 40% 55%), hsl(240 45% 60%));
    --gradient-equals: linear-gradient(135deg, hsl(160 40% 45%), hsl(160 45% 50%));
    --gradient-clear: linear-gradient(135deg, hsl(15 50% 55%), hsl(15 55% 60%));
    --gradient-scientific: linear-gradient(135deg, hsl(200 30% 50%), hsl(200 35% 55%));

    /* Interactive Effects */
    --shadow-glow: 0 0 20px hsl(280 100% 70% / 0.3);
    --shadow-button: 0 4px 15px hsl(280 100% 70% / 0.2);
    --shadow-display: 0 8px 30px hsl(240 40% 8% / 0.4);

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 240 50% 3%;
    --foreground: 280 30% 90%;

    --card: 240 40% 8%;
    --card-foreground: 280 30% 90%;

    --popover: 240 40% 8%;
    --popover-foreground: 280 30% 90%;

    --primary: 280 100% 70%;
    --primary-foreground: 0 0% 100%;

    --secondary: 240 30% 15%;
    --secondary-foreground: 280 30% 90%;

    --muted: 240 30% 15%;
    --muted-foreground: 280 20% 65%;

    --accent: 320 100% 75%;
    --accent-foreground: 0 0% 100%;

    --destructive: 0 100% 67%;
    --destructive-foreground: 0 0% 100%;

    --border: 240 30% 20%;
    --input: 240 30% 20%;
    --ring: 280 100% 70%;

    /* Dark Mode Subtle Calculator Colors */
    --calc-display: 240 40% 5%;
    --calc-display-text: 220 20% 85%;
    --calc-body: 240 30% 8%;
    --calc-button: 240 25% 12%;
    --calc-button-hover: 240 30% 18%;
    --calc-button-text: 220 15% 90%;
    --calc-number: 220 30% 40%;
    --calc-number-hover: 220 35% 45%;
    --calc-operator: 240 35% 50%;
    --calc-operator-hover: 240 40% 55%;
    --calc-equals: 160 35% 45%;
    --calc-equals-hover: 160 40% 50%;
    --calc-clear: 15 40% 50%;
    --calc-clear-hover: 15 45% 55%;
    --calc-scientific: 200 25% 45%;
    --calc-scientific-hover: 200 30% 50%;

    /* Dark Mode Subtle Gradients */
    --gradient-primary: linear-gradient(135deg, hsl(240 35% 50%), hsl(240 40% 55%));
    --gradient-display: linear-gradient(135deg, hsl(240 40% 5%), hsl(240 35% 8%));
    --gradient-body: linear-gradient(135deg, hsl(240 30% 8%), hsl(240 35% 12%));
    --gradient-number: linear-gradient(135deg, hsl(220 30% 40%), hsl(220 35% 45%));
    --gradient-operator: linear-gradient(135deg, hsl(240 35% 50%), hsl(240 40% 55%));
    --gradient-equals: linear-gradient(135deg, hsl(160 35% 45%), hsl(160 40% 50%));
    --gradient-clear: linear-gradient(135deg, hsl(15 40% 50%), hsl(15 45% 55%));
    --gradient-scientific: linear-gradient(135deg, hsl(200 25% 45%), hsl(200 30% 50%));

    /* Dark Mode Interactive Effects */
    --shadow-glow: 0 0 25px hsl(280 100% 70% / 0.4);
    --shadow-button: 0 4px 20px hsl(280 100% 70% / 0.3);
    --shadow-display: 0 8px 40px hsl(240 60% 5% / 0.6);
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    background: linear-gradient(135deg, hsl(var(--background)), hsl(240 100% 97%));
    min-height: 100vh;
  }
}

@layer components {
  .calc-gradient-bg {
    background: var(--gradient-body);
  }
  
  .calc-display-gradient {
    background: var(--gradient-display);
  }
  
  .calc-number-gradient {
    background: var(--gradient-number);
  }
  
  .calc-operator-gradient {
    background: var(--gradient-operator);
  }
  
  .calc-equals-gradient {
    background: var(--gradient-equals);
  }
  
  .calc-clear-gradient {
    background: var(--gradient-clear);
  }
  
  .calc-scientific-gradient {
    background: var(--gradient-scientific);
  }
  
  .calc-button-glow {
    box-shadow: var(--shadow-button);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
  
  .calc-button-glow:hover {
    box-shadow: var(--shadow-glow);
    transform: translateY(-2px);
  }
  
  .calc-display-glow {
    box-shadow: var(--shadow-display);
  }
  
  .calc-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }
}