import React from 'react';
import { motion } from 'framer-motion';
import { CalculatorMode } from '../Calculator';

interface ModeSelectorProps {
  mode: CalculatorMode;
  onModeChange: (mode: CalculatorMode) => void;
}

export const ModeSelector: React.FC<ModeSelectorProps> = ({ mode, onModeChange }) => {
  const modes = [
    { id: 'basic' as CalculatorMode, label: 'Basic' },
    { id: 'standard' as CalculatorMode, label: 'Standard' },
    { id: 'scientific' as CalculatorMode, label: 'Scientific' }
  ];

  return (
    <div className="relative bg-calc-button rounded-xl p-1 border border-white/10 backdrop-blur-sm shadow-lg">
      <div className="relative flex">
        {modes.map((modeOption) => (
          <motion.button
            key={modeOption.id}
            onClick={() => onModeChange(modeOption.id)}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className={`relative flex-1 py-3 px-4 text-sm font-bold rounded-lg transition-all duration-300 ${
              mode === modeOption.id 
                ? 'text-white' 
                : 'text-calc-button-text hover:text-white hover:bg-white/10'
            }`}
          >
            {mode === modeOption.id && (
              <motion.div
                layoutId="activeMode"
                className="absolute inset-0 calc-operator-gradient rounded-lg shadow-lg"
                initial={false}
                transition={{ type: "spring", bounce: 0.2, duration: 0.4 }}
              />
            )}
            <span className="relative z-10">{modeOption.label}</span>
          </motion.button>
        ))}
      </div>
    </div>
  );
};